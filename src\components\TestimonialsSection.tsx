import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote, MapPin } from "lucide-react";
import { useTestimonials } from "@/hooks/useTestimonials";

const TestimonialsSection = () => {
  const { data: testimonials, isLoading } = useTestimonials();

  if (isLoading) {
    return (
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-10 bg-gray-200 rounded w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-200 rounded w-full max-w-3xl mx-auto"></div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Hva Kundene Sier
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Vi er stolte av tilbakemeldingene vi får fra våre kunder. 
            Deres tilfredshet er det beste beviset på kvaliteten av vårt arbeid.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials?.map((testimonial) => (
            <Card 
              key={testimonial.id} 
              className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 relative"
            >
              <CardContent className="p-6">
                <div className="absolute top-4 right-4 text-green-200">
                  <Quote className="h-8 w-8" />
                </div>
                
                <div className="flex items-center gap-1 mb-4">
                  {renderStars(testimonial.rating || 5)}
                </div>
                
                <p className="text-gray-700 mb-4 leading-relaxed italic">
                  "{testimonial.testimonial_text}"
                </p>
                
                <div className="border-t pt-4">
                  <p className="font-semibold text-gray-900 mb-1">
                    {testimonial.customer_name}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    {testimonial.customer_location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{testimonial.customer_location}</span>
                      </div>
                    )}
                    
                    {testimonial.project_type && (
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">
                        {testimonial.project_type}
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
