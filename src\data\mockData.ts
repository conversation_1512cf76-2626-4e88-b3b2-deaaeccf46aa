// Mock data for development when database is not available

export const mockHeroContent = {
  id: '1',
  badge_text: 'Profesjonell Anleggsgartner',
  main_title: 'Ringerike Landskap',
  subtitle: 'Din lokale anleggsgartner i Ringerike-regionen. Vi skaper vakre og funksjonelle uteplasser med høy kvalitet og faglig ekspertise.',
  primary_button_text: 'Se våre prosjekter',
  secondary_button_text: 'Kontakt oss',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

export const mockNavigationItems = [
  { id: '1', name: 'Hjem', href: '/', display_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: '2', name: '<PERSON>je<PERSON><PERSON>', href: '#tjenester', display_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: '3', name: '<PERSON>sje<PERSON><PERSON>', href: '#prosjekter', display_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: '4', name: 'Om oss', href: '#team', display_order: 4, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: '5', name: 'Kontakt', href: '#kontakt', display_order: 5, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
];

export const mockServices = [
  {
    id: '1',
    name: 'Belegg og Platting',
    description: 'Profesjonell legging av belegningsstein, platting og brostein. Vi skaper varige og vakre løsninger for innkjørsler, gangveier og terrasser.',
    icon_name: 'Hammer',
    image_url: null,
    display_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Støttemurer',
    description: 'Bygging av støttemurer i naturstein, betong og andre materialer. Både funksjonelle og estetiske løsninger for skråninger og nivåforskjeller.',
    icon_name: 'Mountain',
    image_url: null,
    display_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'Kantstein og Avgrensning',
    description: 'Montering av kantstein for å skape rene linjer og avgrensninger. Både funksjonelle og dekorative løsninger.',
    icon_name: 'Square',
    image_url: null,
    display_order: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '4',
    name: 'Trapper og Repos',
    description: 'Bygging av trapper og repos i naturstein, betong og andre materialer. Sikre og vakre løsninger for nivåforskjeller.',
    icon_name: 'ArrowUp',
    image_url: null,
    display_order: 4,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '5',
    name: 'Stålarbeider',
    description: 'Sveising og montering av stålkonstruksjoner, rekkverk og andre metallarbeider. Både funksjonelle og dekorative løsninger.',
    icon_name: 'Wrench',
    image_url: null,
    display_order: 5,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '6',
    name: 'Hekk og Beplantning',
    description: 'Planting av hekker, busker og trær. Vi hjelper deg med å skape grønne og levende uteplasser.',
    icon_name: 'TreePine',
    image_url: null,
    display_order: 6,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '7',
    name: 'Ferdigplen',
    description: 'Legging av ferdigplen for rask og vakker gressplen. Profesjonell forberedelse og legging for beste resultat.',
    icon_name: 'Leaf',
    image_url: null,
    display_order: 7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '8',
    name: 'Gravearbeider',
    description: 'Alle typer gravearbeider og terrengmodellering. Vi har utstyr og kompetanse for både små og store prosjekter.',
    icon_name: 'Shovel',
    image_url: null,
    display_order: 8,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

export const mockProjects = [
  {
    id: '1',
    title: 'Moderne Innkjørsel i Hønefoss',
    description: 'Komplett renovering av innkjørsel med nye belegningsstein og kantstein. Prosjektet inkluderte graving, planering og legging av 150 kvm belegg.',
    service_category: 'Belegg',
    image_url: '',
    location: 'Hønefoss',
    completion_date: '2024-05-15',
    display_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    title: 'Støttemur og Terrasse i Hole',
    description: 'Bygging av 25 meter støttemur i naturstein og tilhørende terrasse. Løsningen skapte ny bruksareal og sikret skråningen.',
    service_category: 'Støttemur',
    image_url: '',
    location: 'Hole',
    completion_date: '2024-04-20',
    display_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '3',
    title: 'Hagedesign med Stålrekkverk',
    description: 'Komplett hagedesign med moderne stålrekkverk, beplantning og belysning. Et vakkert og funksjonelt uteområde.',
    service_category: 'Hagedesign',
    image_url: '',
    location: 'Røyse',
    completion_date: '2024-06-10',
    display_order: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

export const mockTeamMembers = [
  {
    id: '1',
    name: 'Jan Eriksen',
    position: 'Daglig leder og anleggsgartner',
    bio: 'Med over 20 års erfaring innen anleggsgartnerfaget, leder Jan Ringerike Landskap med fokus på kvalitet og kundetilfredshet.',
    image_url: null,
    email: '<EMAIL>',
    phone: '+47 123 45 678',
    display_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Kim Andersen',
    position: 'Anleggsgartner og maskinoperatør',
    bio: 'Kim har bred erfaring med alle typer anleggsarbeider og er ekspert på maskinell graving og terrengmodellering.',
    image_url: null,
    email: '<EMAIL>',
    phone: '+47 987 65 432',
    display_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

export const mockTestimonials = [
  {
    id: '1',
    customer_name: 'Kari og Ole Hansen',
    customer_location: 'Hønefoss',
    rating: 5,
    testimonial_text: 'Fantastisk jobb med vår nye innkjørsel! Ringerike Landskap leverte akkurat det vi ønsket oss, og kvaliteten er helt topp.',
    project_type: 'Belegg',
    date_created: '2024-05-20',
    display_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    customer_name: 'Marit Johnsen',
    customer_location: 'Hole',
    rating: 5,
    testimonial_text: 'Profesjonelt team som gjorde en utmerket jobb med støttemuren vår. Anbefaler dem på det sterkeste!',
    project_type: 'Støttemur',
    date_created: '2024-04-25',
    display_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '3',
    customer_name: 'Per og Anne Olsen',
    customer_location: 'Røyse',
    rating: 5,
    testimonial_text: 'Ringerike Landskap transformerte hagen vår fullstendig. Vi er så fornøyde med resultatet!',
    project_type: 'Hagedesign',
    date_created: '2024-06-15',
    display_order: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];
