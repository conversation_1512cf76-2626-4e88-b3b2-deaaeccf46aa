
import { Phone, Mail, MapPin, Clock, TreePine } from "lucide-react";
import { Button } from "@/components/ui/button";

const Footer = () => {
  const contactInfo = [
    { icon: Phone, text: "+47 123 45 678", href: "tel:+4712345678" },
    { icon: Mail, text: "<EMAIL>", href: "mailto:<EMAIL>" },
    { icon: MapPin, text: "Ringerike, Norge", href: "#" },
    { icon: Clock, text: "Man-Fre: 07:00-16:00", href: "#" }
  ];

  const footerSections = [
    {
      title: "Tjenes<PERSON>",
      links: [
        { name: "Belegg og Platting", href: "#tjenester" },
        { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "#tjenester" },
        { name: "<PERSON><PERSON><PERSON>", href: "#tjenester" },
        { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "#tjenester" }
      ]
    },
    {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      links: [
        { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "#" },
        { name: "<PERSON>", href: "#" },
        { name: "<PERSON><PERSON><PERSON><PERSON>", href: "#" },
        { name: "Ringerike", href: "#" }
      ]
    },
    {
      title: "Informasjon",
      links: [
        { name: "Om oss", href: "#team" },
        { name: "Våre prosjekter", href: "#prosjekter" },
        { name: "Kontakt oss", href: "#kontakt" },
        { name: "Tilbud", href: "#" }
      ]
    }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="bg-gradient-to-r from-green-500 to-blue-500 p-2 rounded-lg">
                <TreePine className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold">Ringerike Landskap</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-sm">
              Din lokale anleggsgartner i Ringerike-regionen. Vi skaper vakre og
              funksjonelle uteplasser med høy kvalitet og faglig ekspertise.
            </p>
            <div className="space-y-3">
              {contactInfo.map((contact) => (
                <div key={contact.text} className="flex items-center space-x-3">
                  <contact.icon className="h-4 w-4 text-green-400" />
                  <a
                    href={contact.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {contact.text}
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="text-lg font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Ringerike Landskap. Alle rettigheter forbeholdt.
            </p>
            <div className="flex items-center text-gray-400 text-sm mt-4 md:mt-0">
              <span>Org.nr: 123 456 789</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
