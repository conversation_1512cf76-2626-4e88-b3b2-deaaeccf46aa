
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Code2, 
  Eye, 
  Users, 
  Heart, 
  MessageCircle, 
  Share2,
  Play,
  Pause,
  RotateCcw
} from "lucide-react";

const ComponentShowcase = () => {
  const [likeCount, setLikeCount] = useState(42);
  const [isLiked, setIsLiked] = useState(false);
  const [comments, setComments] = useState(12);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleLike = () => {
    if (isLiked) {
      setLikeCount(prev => prev - 1);
    } else {
      setLikeCount(prev => prev + 1);
    }
    setIsLiked(!isLiked);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const resetDemo = () => {
    setLikeCount(42);
    setIsLiked(false);
    setComments(12);
    setIsPlaying(false);
  };

  return (
    <section id="components" className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Interactive Component Demo
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience how modular components work together seamlessly. 
            Each interaction demonstrates real-time state management.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Live Demo */}
          <Card className="bg-gradient-to-br from-white to-blue-50 border-2 border-blue-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-600" />
                  Live Component Demo
                </CardTitle>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={resetDemo}
                  className="text-gray-600"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Reset
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Media Player Component */}
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">Media Player</h3>
                  <Badge variant={isPlaying ? "default" : "secondary"}>
                    {isPlaying ? "Playing" : "Paused"}
                  </Badge>
                </div>
                <div className="bg-gray-100 h-32 rounded-lg flex items-center justify-center mb-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                      {isPlaying ? (
                        <Pause className="h-8 w-8 text-white" />
                      ) : (
                        <Play className="h-8 w-8 text-white ml-1" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">Sample Video Content</p>
                  </div>
                </div>
                <Button 
                  onClick={togglePlay} 
                  className="w-full"
                  variant={isPlaying ? "secondary" : "default"}
                >
                  {isPlaying ? "Pause" : "Play"}
                </Button>
              </div>

              {/* Social Interaction Component */}
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <h3 className="font-semibold text-gray-900 mb-3">Social Interactions</h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLike}
                      className={`${isLiked ? 'text-red-500' : 'text-gray-500'} hover:text-red-500`}
                    >
                      <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                      {likeCount}
                    </Button>
                    <Button variant="ghost" size="sm" className="text-gray-500">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      {comments}
                    </Button>
                    <Button variant="ghost" size="sm" className="text-gray-500">
                      <Share2 className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Users className="h-4 w-4" />
                    <span>1.2k views</span>
                  </div>
                </div>
              </div>

              {/* State Display */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-medium text-gray-900 mb-2">Current State:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Likes: <span className="font-semibold">{likeCount}</span></div>
                  <div>Comments: <span className="font-semibold">{comments}</span></div>
                  <div>Player: <span className="font-semibold">{isPlaying ? "Playing" : "Paused"}</span></div>
                  <div>Liked: <span className="font-semibold">{isLiked ? "Yes" : "No"}</span></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Code Example */}
          <Card className="bg-gradient-to-br from-gray-900 to-gray-800 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code2 className="h-5 w-5" />
                Component Architecture
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="component" className="w-full">
                <TabsList className="grid w-full grid-cols-3 bg-gray-700">
                  <TabsTrigger value="component" className="text-white">Component</TabsTrigger>
                  <TabsTrigger value="hooks" className="text-white">Hooks</TabsTrigger>
                  <TabsTrigger value="supabase" className="text-white">Supabase</TabsTrigger>
                </TabsList>
                
                <TabsContent value="component" className="mt-4">
                  <pre className="text-sm text-green-400 overflow-x-auto">
{`// MediaPlayer.tsx
const MediaPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  
  return (
    <div className="media-player">
      <Button onClick={() => setIsPlaying(!isPlaying)}>
        {isPlaying ? 'Pause' : 'Play'}
      </Button>
    </div>
  );
};`}
                  </pre>
                </TabsContent>
                
                <TabsContent value="hooks" className="mt-4">
                  <pre className="text-sm text-blue-400 overflow-x-auto">
{`// useLikes.ts
const useLikes = (postId: string) => {
  const [likes, setLikes] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  
  const toggleLike = () => {
    setIsLiked(!isLiked);
    setLikes(prev => isLiked ? prev - 1 : prev + 1);
  };
  
  return { likes, isLiked, toggleLike };
};`}
                  </pre>
                </TabsContent>
                
                <TabsContent value="supabase" className="mt-4">
                  <pre className="text-sm text-purple-400 overflow-x-auto">
{`// With Supabase integration:
const { data: likes, mutate } = useQuery({
  queryKey: ['likes', postId],
  queryFn: () => supabase
    .from('likes')
    .select('count')
    .eq('post_id', postId)
});

// Real-time updates
useEffect(() => {
  const channel = supabase
    .channel('likes')
    .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'likes' },
        () => mutate()
    )
    .subscribe();
}, []);`}
                  </pre>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ComponentShowcase;
