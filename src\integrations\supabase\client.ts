// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mqmrphiqiwsosdfptmls.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1xbXJwaGlxaXdzb3NkZnB0bWxzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMDE4MTIsImV4cCI6MjA2NDc3NzgxMn0.U63JngHKt58VDjAtZ9P4V1fr1tRFeRXY5W2yOrukyeU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);