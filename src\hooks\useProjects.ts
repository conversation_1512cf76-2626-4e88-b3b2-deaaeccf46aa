import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { mockProjects } from "@/data/mockData";

export const useProjects = () => {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('display_order');

        if (error) throw error;
        return data;
      } catch (error) {
        // Fallback to mock data if database is not available
        console.log('Using mock data for projects');
        return mockProjects;
      }
    },
  });
};
