
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { mockHeroContent } from "@/data/mockData";

export const useHeroContent = () => {
  return useQuery({
    queryKey: ['hero-content'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('hero_content')
          .select('*')
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        // Fallback to mock data if database is not available
        console.log('Using mock data for hero content');
        return mockHeroContent;
      }
    },
  });
};
