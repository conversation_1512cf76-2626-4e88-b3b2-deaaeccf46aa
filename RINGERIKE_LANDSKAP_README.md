# Ringerike Landskap - Supabase Demo Website

This project demonstrates how a generic Supabase usecase demo has been transformed into a complete Norwegian landscaping company website using React, TypeScript, and Supabase.

## 🏗️ What Was Transformed

### Original Demo
- Generic "ComponentHub" tech demo
- Features showcasing React components
- Blog posts about development
- Generic UI components

### New Ringerike Landskap Website
- **Norwegian landscaping company website**
- **Services**: Belegg, Støttemurer, Kantstein, Stålarbeider, etc.
- **Project Portfolio**: Showcase of completed landscaping projects
- **Team Section**: Company team members
- **Customer Testimonials**: Real customer reviews
- **Norwegian Content**: All content localized to Norwegian

## 🗄️ Database Schema

### New Tables Created
1. **services** - Landscaping services offered
2. **projects** - Portfolio of completed projects  
3. **team_members** - Company team information
4. **testimonials** - Customer reviews and ratings

### Updated Tables
- **hero_content** - Updated with Norwegian company branding
- **navigation_items** - Norwegian navigation menu

## 🎨 Components Transformed

### Original → New
- `FeaturesSection` → `ServicesSection` - Landscaping services
- `BlogPreview` → `ProjectsGallery` - Project portfolio
- `ComponentShowcase` → `TeamSection` - Team members
- Added `TestimonialsSection` - Customer reviews

### Updated Components
- `Hero` - Norwegian branding and nature theme
- `Navigation` - Norwegian menu items and company branding
- `Footer` - Norwegian contact information and company details

## 🎯 Key Features

### 🌿 Norwegian Landscaping Focus
- **Services**: Belegg og Platting, Støttemurer, Kantstein, Trapper, Stålarbeider, Hekk, Ferdigplen, Gravearbeider
- **Local Areas**: Hønefoss, Hole, Røyse, Ringerike region
- **Norwegian Language**: All content in Norwegian

### 🏢 Company Information
- **Company**: Ringerike Landskap
- **Services**: Professional landscaping and garden construction
- **Team**: Jan Eriksen (Manager), Kim Andersen (Operator)
- **Contact**: Norwegian phone numbers and email addresses

### 💾 Supabase Integration
- **Real-time Data**: All content managed through Supabase
- **Fallback System**: Mock data when database unavailable
- **Type Safety**: Full TypeScript integration
- **MCP Integration**: Supabase MCP server for database management

## 🚀 Technologies Used

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **State Management**: TanStack Query
- **Icons**: Lucide React (nature/construction themed)
- **Development**: Supabase MCP server integration

## 🎨 Design Theme

### Color Scheme
- **Primary**: Green (nature/landscaping theme)
- **Secondary**: Blue (professional/trust)
- **Accent**: Natural earth tones

### Visual Elements
- **Icons**: Construction and nature themed (TreePine, Mountain, Hammer, etc.)
- **Layout**: Clean, professional landscaping company aesthetic
- **Typography**: Clear, readable fonts suitable for Norwegian content

## 📱 Responsive Design

- **Mobile-first**: Optimized for all device sizes
- **Touch-friendly**: Easy navigation on mobile devices
- **Performance**: Fast loading with optimized images

## 🔧 Development Setup

1. **Install Dependencies**: `npm install`
2. **Start Development**: `npm run dev`
3. **Configure Supabase MCP**: Set up access token for database management
4. **Database Setup**: Apply migrations for Ringerike Landskap tables

## 📊 Mock Data

The application includes comprehensive mock data for development:
- 8 landscaping services
- 3 sample projects
- 2 team members  
- 3 customer testimonials
- Norwegian navigation and hero content

## 🌐 Live Demo

The website showcases:
- **Professional landscaping services**
- **Portfolio of completed projects**
- **Team member profiles**
- **Customer testimonials**
- **Norwegian company branding**
- **Contact information**

## 🎯 Business Value

This transformation demonstrates how a generic tech demo can be converted into a real business website with:
- **Industry-specific content**
- **Local market focus**
- **Professional branding**
- **Customer-focused messaging**
- **Scalable database architecture**

Perfect for showcasing Supabase capabilities in a real-world business context!
