import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { mockTestimonials } from "@/data/mockData";

export const useTestimonials = () => {
  return useQuery({
    queryKey: ['testimonials'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('testimonials')
          .select('*')
          .order('display_order');

        if (error) throw error;
        return data;
      } catch (error) {
        // Fallback to mock data if database is not available
        console.log('Using mock data for testimonials');
        return mockTestimonials;
      }
    },
  });
};
