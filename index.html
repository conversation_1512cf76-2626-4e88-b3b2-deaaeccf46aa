
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ComponentHub - Modern React Component Architecture</title>
    <meta name="description" content="Discover how modular React components and Supabase integration create flexible, maintainable applications. See live examples and best practices." />
    <meta name="author" content="ComponentHub" />

    <meta property="og:title" content="ComponentHub - Modern React Component Architecture" />
    <meta property="og:description" content="Discover how modular React components and Supabase integration create flexible, maintainable applications." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
