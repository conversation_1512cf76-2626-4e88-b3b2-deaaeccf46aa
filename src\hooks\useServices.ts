import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { mockServices } from "@/data/mockData";

export const useServices = () => {
  return useQuery({
    queryKey: ['services'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('services')
          .select('*')
          .order('display_order');

        if (error) throw error;
        return data;
      } catch (error) {
        // Fallback to mock data if database is not available
        console.log('Using mock data for services');
        return mockServices;
      }
    },
  });
};
