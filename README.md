# Ringerike Landskap - Professional Landscaping Website

A modern, responsive website for Ringerike Landskap, a Norwegian landscaping company. Built with React, TypeScript, and Supabase to showcase professional landscaping services in the Ringerike region.

## 🌿 About Ringerike Landskap

Ringerike Landskap is a professional landscaping company serving the Ringerike region in Norway. We specialize in creating beautiful and functional outdoor spaces with high quality and professional expertise.

## 🚀 Features

- **Norwegian Landscaping Services**: Complete portfolio of landscaping services
- **Project Gallery**: Showcase of completed landscaping projects
- **Team Profiles**: Meet our professional landscaping team
- **Customer Testimonials**: Real reviews from satisfied customers
- **Responsive Design**: Optimized for all devices
- **Real-time Content**: Dynamic content management through Supabase

## 🏗️ Services Offered

- **Belegg og Platting** - Professional stone laying and paving
- **Støttemurer** - Retaining walls in natural stone and concrete
- **Ka<PERSON>tein og Avgrensning** - Curb stones and garden borders
- **Trapper og Repos** - Stairs and landings in various materials
- **Stålarbeider** - Steel construction and metalwork
- **Hek<PERSON> og Beplantning** - Hedges and landscaping plants
- **Ferdigplen** - Ready-made lawn installation
- **Gravearbeider** - Excavation and terrain modeling

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **State Management**: TanStack Query (React Query)
- **Icons**: Lucide React (nature/construction themed)
- **Language**: Norwegian (Bokmål)

## 📦 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── Hero.tsx        # Hero section with company branding
│   ├── ServicesSection.tsx # Landscaping services
│   ├── ProjectsGallery.tsx # Project portfolio
│   ├── TeamSection.tsx     # Team member profiles
│   ├── TestimonialsSection.tsx # Customer reviews
│   ├── Navigation.tsx      # Norwegian navigation
│   └── Footer.tsx          # Company contact info
├── hooks/              # Custom React hooks for data fetching
│   ├── useServices.ts      # Landscaping services data
│   ├── useProjects.ts      # Project portfolio data
│   ├── useTeamMembers.ts   # Team member data
│   ├── useTestimonials.ts  # Customer testimonials
│   ├── useHeroContent.ts   # Hero section content
│   └── useNavigationItems.ts # Navigation menu
├── integrations/       # Supabase integration
│   └── supabase/       # Client and TypeScript types
└── pages/              # Page components
```

## 🗄️ Database Schema

The application uses the following Supabase tables:

- **services**: Landscaping services offered by the company
- **projects**: Portfolio of completed landscaping projects
- **team_members**: Company team member profiles
- **testimonials**: Customer reviews and ratings
- **hero_content**: Dynamic hero section content
- **navigation_items**: Website navigation menu

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ringerike-landskap-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Configure your Supabase project
   - Update the client configuration with your project details
   - The database tables and data are already set up

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:8081` to see the website

## 🎨 Design Theme

- **Colors**: Green and blue (nature/professional theme)
- **Typography**: Clean, readable fonts suitable for Norwegian content
- **Icons**: Construction and nature themed (TreePine, Mountain, Hammer, etc.)
- **Layout**: Professional landscaping company aesthetic
- **Language**: All content in Norwegian (Bokmål)

## 📱 Responsive Design

- **Mobile**: Touch-friendly navigation and optimized layouts
- **Tablet**: Adaptive grid layouts for medium screens
- **Desktop**: Full-featured experience with hover effects

## 🌍 Service Areas

- Hønefoss
- Hole
- Røyse
- Ringerike region

## 📞 Contact Information

- **Company**: Ringerike Landskap
- **Phone**: +47 123 45 678
- **Email**: <EMAIL>
- **Location**: Ringerike, Norge
- **Hours**: Man-Fre: 07:00-16:00

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Deploy the `dist` folder** to your hosting service

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [React](https://reactjs.org/) - UI library
- [Vite](https://vitejs.dev/) - Build tool
- [Supabase](https://supabase.com/) - Backend as a Service
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [Lucide React](https://lucide.dev/) - Icon library
