
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { mockNavigationItems } from "@/data/mockData";

export const useNavigationItems = () => {
  return useQuery({
    queryKey: ['navigation-items'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('navigation_items')
          .select('*')
          .order('display_order');

        if (error) throw error;
        return data;
      } catch (error) {
        // Fallback to mock data if database is not available
        console.log('Using mock data for navigation items');
        return mockNavigationItems;
      }
    },
  });
};
