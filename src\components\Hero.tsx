
import { But<PERSON> } from "@/components/ui/button";
import { Arrow<PERSON><PERSON>, Sparkles, TreePine, Mountain } from "lucide-react";
import { useHeroContent } from "@/hooks/useHeroContent";

const Hero = () => {
  const { data: heroContent, isLoading } = useHeroContent();

  if (isLoading) {
    return (
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-8"></div>
              <div className="h-16 bg-gray-200 rounded w-96 mx-auto mb-6"></div>
              <div className="h-6 bg-gray-200 rounded w-full max-w-3xl mx-auto mb-8"></div>
              <div className="flex gap-4 justify-center">
                <div className="h-12 bg-gray-200 rounded w-40"></div>
                <div className="h-12 bg-gray-200 rounded w-40"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!heroContent) return null;

  return (
    <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-green-50 to-blue-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-green-100 rounded-full text-green-700 text-sm font-medium mb-8">
            <TreePine className="h-4 w-4 mr-2" />
            {heroContent.badge_text}
          </div>

          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              {heroContent.main_title}
            </span>
          </h1>

          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            {heroContent.subtitle}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
              <Mountain className="h-5 w-5 mr-2" />
              {heroContent.primary_button_text}
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
            <Button variant="outline" size="lg">
              {heroContent.secondary_button_text}
            </Button>
          </div>
        </div>

        {/* Floating elements for visual appeal - nature themed */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-green-200 rounded-full opacity-20 animate-bounce"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-emerald-200 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '2s' }}></div>
      </div>
    </section>
  );
};

export default Hero;
